#!/usr/bin/env python3
"""
Targon 注册机【躺平版】
使用固定邮箱地址 <EMAIL>
基于 Cloudflare 绑定的 551543.xyz 域名的临时邮箱服务
所有功能集中在一个文件中
"""

import json
import time
import random
import string
import re
import uuid
import httpx
import pyotp
import logging
from datetime import datetime

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('targon_registration.log', encoding='utf-8'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

class TargonRegistrar:
    """
    专业版 Targon 注册器
    使用日志记录和更好的错误处理
    """
    def __init__(self):
        self.client = httpx.Client(
            timeout=60.0,
            headers={
                'User-Agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
                'Accept': '*/*',
                'Accept-Language': 'zh-CN,zh;q=0.9,en-US;q=0.8,en;q=0.7',
                'Content-Type': 'application/json',
                'sec-ch-ua': '"Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"',
                'sec-ch-ua-mobile': '?0',
                'sec-ch-ua-platform': '"macOS"',
                'sec-fetch-dest': 'empty',
                'sec-fetch-mode': 'cors',
                'sec-fetch-site': 'same-origin',
                'x-trpc-source': 'react'
            }
        )
        self.session_cookie = None
        self.email_address = "<EMAIL>"  # 使用固定邮箱地址
        self.keys_file = "api_keys.txt"

    def register_account(self, password):
        """
        注册Targon账户

        Args:
            password: 密码

        Returns:
            bool: 注册是否成功
        """
        try:
            logger.info(f"开始注册账户: {self.email_address}")

            # 构造注册请求数据
            register_data = {
                "0": {
                    "json": {
                        "email": self.email_address,
                        "password": password,
                        "password2": password
                    }
                }
            }

            # 发送注册请求
            response = self.client.post(
                "https://targon.com/api/trpc/account.createAccount?batch=1",
                json=register_data
            )

            logger.info(f"注册请求响应状态: {response.status_code}")

            if response.status_code == 200:
                logger.info(f"账户注册成功: {self.email_address}")
                return True
            else:
                logger.error(f"账户注册失败: {response.status_code} - {response.text}")
                return False

        except Exception as e:
            logger.error(f"注册过程中发生错误: {e}")
            return False

    def get_activation_link(self):
        """
        获取激活链接 - 使用固定邮箱的mailto.plus服务
        """
        try:
            logger.info("等待激活邮件...")
            print("📬 等待激活邮件...")
            print("⚠️  请手动检查 <EMAIL> 邮箱中的激活邮件")
            print("🔗 由于使用固定邮箱，需要手动获取激活链接")

            # 由于使用固定邮箱，这里需要用户手动处理激活链接
            activation_link = input("请输入从邮箱中获取的激活链接: ").strip()

            if activation_link and 'email-verification' in activation_link:
                logger.info("成功获取激活链接")
                print("🔗 成功获取激活链接")
                return activation_link
            else:
                logger.error("无效的激活链接")
                print("❌ 无效的激活链接")
                return None

        except Exception as e:
            logger.error(f"获取激活链接异常: {e}")
            return None

    def activate_email(self, activation_link):
        """
        激活邮箱

        Args:
            activation_link: 激活链接

        Returns:
            bool: 激活是否成功
        """
        try:
            logger.info(f"开始激活邮箱: {activation_link}")
            print("🔗 开始激活邮箱...")

            # 设置激活请求的headers
            activation_headers = {
                'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7',
                'Accept-Language': 'zh-CN,zh;q=0.9,en-US;q=0.8,en;q=0.7',
                'sec-ch-ua': '"Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"',
                'sec-ch-ua-mobile': '?0',
                'sec-ch-ua-platform': '"macOS"',
                'sec-fetch-dest': 'document',
                'sec-fetch-mode': 'navigate',
                'sec-fetch-site': 'none',
                'sec-fetch-user': '?1',
                'upgrade-insecure-requests': '1',
                'User-Agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36'
            }

            # 手动处理重定向以确保获取所有cookie
            current_url = activation_link
            max_redirects = 5
            redirect_count = 0

            while redirect_count < max_redirects:
                response = self.client.get(
                    current_url,
                    headers=activation_headers,
                    follow_redirects=False  # 不自动跟随重定向
                )

                logger.info(f"请求 {current_url} 响应状态: {response.status_code}")

                # 检查Set-Cookie响应头
                set_cookie_headers = response.headers.get_list('set-cookie')
                if set_cookie_headers:
                    logger.info(f"收到Set-Cookie头: {len(set_cookie_headers)}个")
                    for set_cookie in set_cookie_headers:
                        if 'auth_session=' in set_cookie:
                            # 提取auth_session的值
                            parts = set_cookie.split(';')
                            for part in parts:
                                if part.strip().startswith('auth_session='):
                                    self.session_cookie = part.strip().split('=', 1)[1]
                                    logger.info(f"从Set-Cookie头获取到session cookie")
                                    print("🍪 获取到登录凭证")
                                    break

                # 检查并收集cookie（备用方法）
                try:
                    for cookie in response.cookies:
                        if hasattr(cookie, 'name') and cookie.name == 'auth_session':
                            if not self.session_cookie:  # 如果还没有从Set-Cookie头获取到
                                self.session_cookie = cookie.value
                                logger.info(f"从cookies获取到session cookie")
                                print("🍪 获取到登录凭证")
                except Exception as e:
                    logger.debug(f"检查cookies时出错（这是正常的）: {e}")

                # 如果是重定向状态码，获取Location头并继续
                if response.status_code in [301, 302, 307, 308]:
                    location = response.headers.get('Location')
                    if not location:
                        logger.error("重定向响应中没有Location头")
                        break

                    # 处理相对URL
                    if location.startswith('/'):
                        from urllib.parse import urljoin
                        current_url = urljoin('https://targon.com', location)
                    else:
                        current_url = location

                    logger.info(f"重定向到: {current_url}")
                    redirect_count += 1
                    continue

                elif response.status_code == 200:
                    logger.info("激活流程完成")
                    print("✅ 邮箱激活成功")
                    break
                else:
                    logger.error(f"激活失败，状态码: {response.status_code}")
                    print(f"❌ 激活失败: {response.status_code}")
                    return False

            if redirect_count >= max_redirects:
                logger.error("重定向次数过多")
                print("❌ 重定向次数过多")
                return False

            # 检查是否获取到session cookie
            if self.session_cookie:
                logger.info("邮箱激活成功，已获取session")
                return True
            else:
                logger.warning("邮箱激活成功但未获取到session cookie")
                return True

        except Exception as e:
            logger.error(f"激活过程中发生错误: {e}")
            print(f"❌ 激活异常: {e}")
            return False

    def create_2fa(self):
        """
        创建2FA设置

        Returns:
            dict: 包含2FA URI和密钥的字典，失败返回None
        """
        try:
            logger.info("开始创建2FA设置")
            print("🔐 开始设置2FA...")

            if not self.session_cookie:
                logger.error("没有有效的session cookie")
                print("❌ 没有有效的登录凭证")
                return None

            # 设置请求headers和cookies
            headers = self.client.headers.copy()
            headers.update({
                'Referer': 'https://targon.com/two-factor-auth',
                'Origin': 'https://targon.com'
            })

            cookies = {'auth_session': self.session_cookie}

            # 发送创建2FA请求
            response = self.client.get(
                "https://targon.com/api/trpc/account.createTwoFactorURI?batch=1&input=%7B%220%22%3A%7B%22json%22%3Anull%2C%22meta%22%3A%7B%22values%22%3A%5B%22undefined%22%5D%7D%7D%7D",
                headers=headers,
                cookies=cookies
            )

            logger.info(f"创建2FA请求响应状态: {response.status_code}")

            if response.status_code == 200:
                response_data = response.json()

                if response_data and len(response_data) > 0:
                    result_data = response_data[0].get('result', {}).get('data', {}).get('json', {})

                    if result_data:
                        two_factor_secret = result_data.get('twoFactorSecret')
                        manual_code = result_data.get('manualCode')
                        uri = result_data.get('uri')

                        logger.info(f"2FA创建成功")
                        logger.info(f"Manual Code: {manual_code}")

                        return {
                            'two_factor_secret': two_factor_secret,
                            'manual_code': manual_code,
                            'uri': uri
                        }

                logger.error("2FA响应数据格式不正确")
                print("❌ 创建2FA失败: 响应数据格式不正确")
                return None
            else:
                logger.error(f"创建2FA失败: {response.status_code} - {response.text}")
                print(f"❌ 创建2FA失败: {response.status_code}")
                return None

        except Exception as e:
            logger.error(f"创建2FA过程中发生错误: {e}")
            print(f"❌ 2FA设置异常: {e}")
            return None

    def enable_2fa(self, two_factor_secret, manual_code):
        """
        启用2FA

        Args:
            two_factor_secret: 2FA密钥
            manual_code: 手动输入代码

        Returns:
            bool: 启用是否成功
        """
        try:
            logger.info("开始启用2FA")

            if not self.session_cookie:
                logger.error("没有有效的session cookie")
                print("❌ 没有有效的登录凭证")
                return False

            # 生成TOTP验证码
            totp = pyotp.TOTP(manual_code)
            otp_code = totp.now()
            logger.info(f"生成的OTP码: {otp_code}")

            # 设置请求headers和cookies
            headers = self.client.headers.copy()
            headers.update({
                'Referer': 'https://targon.com/two-factor-auth',
                'Origin': 'https://targon.com'
            })

            cookies = {'auth_session': self.session_cookie}

            # 构造启用2FA请求数据
            enable_2fa_data = {
                "0": {
                    "json": {
                        "otp": otp_code,
                        "twoFactorSecret": two_factor_secret
                    }
                }
            }

            # 发送启用2FA请求
            response = self.client.post(
                "https://targon.com/api/trpc/account.enable2FA?batch=1",
                json=enable_2fa_data,
                headers=headers,
                cookies=cookies
            )

            logger.info(f"启用2FA请求响应状态: {response.status_code}")

            if response.status_code == 200:
                logger.info("2FA启用成功")
                print("✅ 2FA设置成功")
                return True
            else:
                logger.error(f"启用2FA失败: {response.status_code} - {response.text}")
                print(f"❌ 启用2FA失败: {response.status_code}")
                return False

        except Exception as e:
            logger.error(f"启用2FA过程中发生错误: {e}")
            print(f"❌ 2FA设置异常: {e}")
            return False

    def get_api_keys(self):
        """
        获取API密钥

        Returns:
            list: API密钥列表，失败返回None
        """
        try:
            logger.info("开始获取API密钥")
            print("🔑 获取API密钥...")

            if not self.session_cookie:
                logger.error("没有有效的session cookie")
                print("❌ 没有有效的登录凭证")
                return None

            # 设置请求headers和cookies
            headers = self.client.headers.copy()
            headers.update({
                'Referer': 'https://targon.com/settings'
            })

            cookies = {'auth_session': self.session_cookie}

            # 构造查询URL（包含多个API调用）
            query_url = f"https://targon.com/api/trpc/keys.getApiKeys,model.getPopularModels,account.getUserBookmarks,account.getUserInterest,account.getUserSubscription,account.check2FA,account.getTaoPrice,account.getAlphaPrice,notification.getNotifications?batch=1&input=%7B%220%22%3A%7B%22json%22%3Anull%2C%22meta%22%3A%7B%22values%22%3A%5B%22undefined%22%5D%7D%7D%2C%221%22%3A%7B%22json%22%3A%7B%22days%22%3A30%2C%22limit%22%3A3%7D%7D%2C%222%22%3A%7B%22json%22%3Anull%2C%22meta%22%3A%7B%22values%22%3A%5B%22undefined%22%5D%7D%7D%2C%223%22%3A%7B%22json%22%3Anull%2C%22meta%22%3A%7B%22values%22%3A%5B%22undefined%22%5D%7D%7D%2C%224%22%3A%7B%22json%22%3Anull%2C%22meta%22%3A%7B%22values%22%3A%5B%22undefined%22%5D%7D%7D%2C%225%22%3A%7B%22json%22%3A%7B%22email%22%3A%22{self.email_address}%22%7D%7D%2C%226%22%3A%7B%22json%22%3Anull%2C%22meta%22%3A%7B%22values%22%3A%5B%22undefined%22%5D%7D%7D%2C%227%22%3A%7B%22json%22%3Anull%2C%22meta%22%3A%7B%22values%22%3A%5B%22undefined%22%5D%7D%7D%2C%228%22%3A%7B%22json%22%3Anull%2C%22meta%22%3A%7B%22values%22%3A%5B%22undefined%22%5D%7D%7D%7D"

            # 发送获取API密钥请求
            response = self.client.get(
                query_url,
                headers=headers,
                cookies=cookies
            )

            logger.info(f"获取API密钥请求响应状态: {response.status_code}")

            if response.status_code == 200:
                response_data = response.json()

                if response_data and len(response_data) > 0:
                    # 第一个元素是API密钥信息
                    api_keys_data = response_data[0].get('result', {}).get('data', {}).get('json', [])

                    if api_keys_data:
                        logger.info(f"成功获取到 {len(api_keys_data)} 个API密钥")
                        print(f"✅ 获取到 {len(api_keys_data)} 个API密钥")
                        for key_info in api_keys_data:
                            logger.info(f"API密钥: {key_info.get('name')} - {key_info.get('key')}")
                        return api_keys_data
                    else:
                        logger.warning("未找到API密钥")
                        print("⚠️ 未找到API密钥")
                        return []

                logger.error("API密钥响应数据格式不正确")
                print("❌ 获取密钥失败: 响应数据格式不正确")
                return None
            else:
                logger.error(f"获取API密钥失败: {response.status_code} - {response.text}")
                print(f"❌ 获取密钥失败: {response.status_code}")
                return None

        except Exception as e:
            logger.error(f"获取API密钥过程中发生错误: {e}")
            print(f"❌ 获取密钥异常: {e}")
            return None

    def generate_password(self):
        """生成随机密码"""
        length = 12
        chars = string.ascii_letters + string.digits + "!@#$%^&*"
        return ''.join(random.choice(chars) for _ in range(length))

    def save_keys(self, api_keys):
        """保存API密钥到文件"""
        try:
            # 读取现有密钥
            existing_keys = set()
            try:
                with open(self.keys_file, 'r', encoding='utf-8') as f:
                    existing_keys = {line.strip() for line in f if line.strip()}
            except FileNotFoundError:
                pass

            # 保存新密钥
            new_keys = []
            for key_info in api_keys:
                key = key_info.get('key')
                if key and key not in existing_keys:
                    new_keys.append(key)

            if new_keys:
                with open(self.keys_file, 'a', encoding='utf-8') as f:
                    for key in new_keys:
                        f.write(key + '\n')
                logger.info(f"保存了 {len(new_keys)} 个新密钥到 {self.keys_file}")
                print(f"💾 保存了 {len(new_keys)} 个新密钥到 {self.keys_file}")
            else:
                logger.info("没有新密钥需要保存")
                print("ℹ️ 没有新密钥需要保存")

        except Exception as e:
            logger.error(f"保存密钥异常: {e}")
            print(f"❌ 保存密钥异常: {e}")

    def register_single_account(self):
        """注册单个账户的完整流程"""
        try:
            logger.info(f"开始注册新账户 - {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
            print(f"\n🎯 开始注册新账户 - {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
            print(f"📧 使用邮箱: {self.email_address}")

            # 1. 生成密码
            password = self.generate_password()
            logger.info(f"生成密码: {password}")
            print(f"🔐 生成密码: {password}")

            # 2. 注册账户
            if not self.register_account(password):
                return False

            # 3. 获取激活链接
            activation_link = self.get_activation_link()
            if not activation_link:
                return False

            # 4. 激活邮箱
            if not self.activate_email(activation_link):
                return False

            # 5. 创建2FA
            tfa_data = self.create_2fa()
            if not tfa_data:
                return False

            # 6. 启用2FA
            if not self.enable_2fa(tfa_data['two_factor_secret'], tfa_data['manual_code']):
                return False

            # 7. 获取API密钥
            api_keys = self.get_api_keys()
            if not api_keys:
                logger.error("未获取到API密钥")
                print("❌ 未获取到API密钥")
                return False

            # 8. 保存密钥
            self.save_keys(api_keys)

            # 9. 显示成功信息
            logger.info("账户注册完成!")
            print("\n🎉 账户注册完成!")
            print(f"📧 邮箱: {self.email_address}")
            print(f"🔐 密码: {password}")
            print(f"🔑 API密钥:")
            for key_info in api_keys:
                key = key_info.get('key', '')
                print(f"   {key[:15]}...{key[-8:] if len(key) > 23 else key}")

            return True

        except Exception as e:
            logger.error(f"注册流程异常: {e}")
            print(f"❌ 注册流程异常: {e}")
            return False

    def __del__(self):
        """确保HTTP客户端在对象销毁时关闭"""
        try:
            self.client.close()
        except:
            pass

class TargonSimple:
    def __init__(self):
        self.visitor_id = str(uuid.uuid4())
        self.email_address = "<EMAIL>"  # 使用固定邮箱地址
        self.session_cookie = None
        self.keys_file = "api_keys.txt"
        
        # HTTP客户端配置
        self.client = httpx.Client(
            timeout=60.0,
            headers={
                'User-Agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
                'Accept': '*/*',
                'Accept-Language': 'zh-CN,zh;q=0.9,en-US;q=0.8,en;q=0.7',
                'sec-ch-ua': '"Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"',
                'sec-ch-ua-mobile': '?0',
                'sec-ch-ua-platform': '"macOS"',
                'sec-fetch-dest': 'empty',
                'sec-fetch-mode': 'cors',
                'sec-fetch-site': 'same-origin',
                'sec-gpc': '1',
                'visitor-id': self.visitor_id
            }
        )
    
    def generate_password(self):
        """生成随机密码"""
        length = 12
        chars = string.ascii_letters + string.digits + "!@#$%^&*"
        return ''.join(random.choice(chars) for _ in range(length))
    
    def get_email_address(self):
        """使用固定邮箱地址"""
        try:
            print("📧 使用固定邮箱地址...")
            print(f"✅ 邮箱地址: {self.email_address}")
            return True

        except Exception as e:
            print(f"❌ 邮箱设置异常: {e}")
            return False
    
    def register_account(self, password):
        """注册Targon账户"""
        try:
            print("🚀 开始注册账户...")
            
            register_data = {
                "0": {
                    "json": {
                        "email": self.email_address,
                        "password": password,
                        "password2": password
                    }
                }
            }
            
            response = self.client.post(
                "https://targon.com/api/trpc/account.createAccount?batch=1",
                json=register_data,
                headers={
                    'Content-Type': 'application/json',
                    'x-trpc-source': 'react'
                }
            )
            
            if response.status_code == 200:
                print("✅ 账户注册成功")
                return True
            else:
                print(f"❌ 注册失败: {response.status_code}")
                return False
                
        except Exception as e:
            print(f"❌ 注册异常: {e}")
            return False
    
    def get_activation_link(self, max_attempts=15, delay=3):
        """获取激活链接 - 使用固定邮箱的mailto.plus服务"""
        try:
            print("📬 等待激活邮件...")
            print("⚠️  请手动检查 <EMAIL> 邮箱中的激活邮件")
            print("🔗 由于使用固定邮箱，需要手动获取激活链接")

            # 由于使用固定邮箱，这里需要用户手动处理激活链接
            # 可以考虑添加手动输入激活链接的功能
            activation_link = input("请输入从邮箱中获取的激活链接: ").strip()

            if activation_link and 'email-verification' in activation_link:
                print("🔗 成功获取激活链接")
                return activation_link
            else:
                print("❌ 无效的激活链接")
                return None

        except Exception as e:
            print(f"❌ 获取激活链接异常: {e}")
            return None
    
    def activate_email(self, activation_link):
        """激活邮箱"""
        try:
            print("🔗 开始激活邮箱...")
            
            # 设置激活请求头
            activation_headers = {
                'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8',
                'Accept-Language': 'zh-CN,zh;q=0.9,en-US;q=0.8,en;q=0.7',
                'sec-fetch-dest': 'document',
                'sec-fetch-mode': 'navigate',
                'sec-fetch-site': 'none',
                'sec-fetch-user': '?1',
                'upgrade-insecure-requests': '1',
                'User-Agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36'
            }
            
            # 手动处理重定向
            current_url = activation_link
            max_redirects = 5
            redirect_count = 0
            
            while redirect_count < max_redirects:
                response = self.client.get(
                    current_url,
                    headers=activation_headers,
                    follow_redirects=False
                )
                
                # 检查Set-Cookie头
                set_cookie_headers = response.headers.get_list('set-cookie')
                for set_cookie in set_cookie_headers:
                    if 'auth_session=' in set_cookie:
                        # 提取auth_session的值
                        parts = set_cookie.split(';')
                        for part in parts:
                            if part.strip().startswith('auth_session='):
                                self.session_cookie = part.strip().split('=', 1)[1]
                                print("🍪 获取到登录凭证")
                                break
                
                # 处理重定向
                if response.status_code in [301, 302, 307, 308]:
                    location = response.headers.get('Location')
                    if not location:
                        break
                    
                    # 处理相对URL
                    if location.startswith('/'):
                        from urllib.parse import urljoin
                        current_url = urljoin('https://targon.com', location)
                    else:
                        current_url = location
                    
                    redirect_count += 1
                    continue
                
                elif response.status_code == 200:
                    print("✅ 邮箱激活成功")
                    return True
                else:
                    print(f"❌ 激活失败: {response.status_code}")
                    return False
            
            print("❌ 重定向次数过多")
            return False
            
        except Exception as e:
            print(f"❌ 激活异常: {e}")
            return False
    
    def setup_2fa(self):
        """设置2FA"""
        try:
            if not self.session_cookie:
                print("❌ 没有有效的登录凭证")
                return False
            
            print("🔐 开始设置2FA...")
            
            # 创建2FA
            headers = {
                'Content-Type': 'application/json',
                'x-trpc-source': 'react',
                'Referer': 'https://targon.com/two-factor-auth'
            }
            cookies = {'auth_session': self.session_cookie}
            
            # 获取2FA密钥
            response = self.client.get(
                "https://targon.com/api/trpc/account.createTwoFactorURI?batch=1&input=%7B%220%22%3A%7B%22json%22%3Anull%2C%22meta%22%3A%7B%22values%22%3A%5B%22undefined%22%5D%7D%7D%7D",
                headers=headers,
                cookies=cookies
            )
            
            if response.status_code != 200:
                print(f"❌ 创建2FA失败: {response.status_code}")
                return False
            
            response_data = response.json()
            result_data = response_data[0]['result']['data']['json']
            two_factor_secret = result_data['twoFactorSecret']
            manual_code = result_data['manualCode']
            
            # 生成TOTP验证码
            totp = pyotp.TOTP(manual_code)
            otp_code = totp.now()
            
            # 启用2FA
            enable_data = {
                "0": {
                    "json": {
                        "otp": otp_code,
                        "twoFactorSecret": two_factor_secret
                    }
                }
            }
            
            response = self.client.post(
                "https://targon.com/api/trpc/account.enable2FA?batch=1",
                json=enable_data,
                headers=headers,
                cookies=cookies
            )
            
            if response.status_code == 200:
                print("✅ 2FA设置成功")
                return True
            else:
                print(f"❌ 启用2FA失败: {response.status_code}")
                return False
                
        except Exception as e:
            print(f"❌ 2FA设置异常: {e}")
            return False
    
    def get_api_keys(self):
        """获取API密钥"""
        try:
            if not self.session_cookie:
                print("❌ 没有有效的登录凭证")
                return []
            
            print("🔑 获取API密钥...")
            
            headers = {
                'x-trpc-source': 'react',
                'Referer': 'https://targon.com/settings'
            }
            cookies = {'auth_session': self.session_cookie}
            
            # 构造查询URL
            query_url = f"https://targon.com/api/trpc/keys.getApiKeys,model.getPopularModels,account.getUserBookmarks,account.getUserInterest,account.getUserSubscription,account.check2FA,account.getTaoPrice,account.getAlphaPrice,notification.getNotifications?batch=1&input=%7B%220%22%3A%7B%22json%22%3Anull%2C%22meta%22%3A%7B%22values%22%3A%5B%22undefined%22%5D%7D%7D%2C%221%22%3A%7B%22json%22%3A%7B%22days%22%3A30%2C%22limit%22%3A3%7D%7D%2C%222%22%3A%7B%22json%22%3Anull%2C%22meta%22%3A%7B%22values%22%3A%5B%22undefined%22%5D%7D%7D%2C%223%22%3A%7B%22json%22%3Anull%2C%22meta%22%3A%7B%22values%22%3A%5B%22undefined%22%5D%7D%7D%2C%224%22%3A%7B%22json%22%3Anull%2C%22meta%22%3A%7B%22values%22%3A%5B%22undefined%22%5D%7D%7D%2C%225%22%3A%7B%22json%22%3A%7B%22email%22%3A%22{self.email_address}%22%7D%7D%2C%226%22%3A%7B%22json%22%3Anull%2C%22meta%22%3A%7B%22values%22%3A%5B%22undefined%22%5D%7D%7D%2C%227%22%3A%7B%22json%22%3Anull%2C%22meta%22%3A%7B%22values%22%3A%5B%22undefined%22%5D%7D%7D%2C%228%22%3A%7B%22json%22%3Anull%2C%22meta%22%3A%7B%22values%22%3A%5B%22undefined%22%5D%7D%7D%7D"
            
            response = self.client.get(
                query_url,
                headers=headers,
                cookies=cookies
            )
            
            if response.status_code == 200:
                response_data = response.json()
                api_keys_data = response_data[0]['result']['data']['json']
                
                if api_keys_data:
                    print(f"✅ 获取到 {len(api_keys_data)} 个API密钥")
                    return api_keys_data
                else:
                    print("⚠️ 未找到API密钥")
                    return []
            else:
                print(f"❌ 获取密钥失败: {response.status_code}")
                return []
                
        except Exception as e:
            print(f"❌ 获取密钥异常: {e}")
            return []
    
    def save_keys(self, api_keys):
        """保存API密钥到文件"""
        try:
            # 读取现有密钥
            existing_keys = set()
            try:
                with open(self.keys_file, 'r', encoding='utf-8') as f:
                    existing_keys = {line.strip() for line in f if line.strip()}
            except FileNotFoundError:
                pass
            
            # 保存新密钥
            new_keys = []
            for key_info in api_keys:
                key = key_info.get('key')
                if key and key not in existing_keys:
                    new_keys.append(key)
            
            if new_keys:
                with open(self.keys_file, 'a', encoding='utf-8') as f:
                    for key in new_keys:
                        f.write(key + '\n')
                print(f"💾 保存了 {len(new_keys)} 个新密钥到 {self.keys_file}")
            else:
                print("ℹ️ 没有新密钥需要保存")
                
        except Exception as e:
            print(f"❌ 保存密钥异常: {e}")
    
    def register_single_account(self):
        """注册单个账户的完整流程"""
        try:
            print(f"\n🎯 开始注册新账户 - {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
            
            # 1. 获取邮箱
            if not self.get_email_address():
                return False
            
            # 2. 生成密码
            password = self.generate_password()
            print(f"🔐 生成密码: {password}")
            
            # 3. 注册账户
            if not self.register_account(password):
                return False
            
            # 4. 获取激活链接
            activation_link = self.get_activation_link()
            if not activation_link:
                return False
            
            # 5. 激活邮箱
            if not self.activate_email(activation_link):
                return False
            
            # 6. 设置2FA
            if not self.setup_2fa():
                return False
            
            # 7. 获取API密钥
            api_keys = self.get_api_keys()
            if not api_keys:
                print("❌ 未获取到API密钥")
                return False
            
            # 8. 保存密钥
            self.save_keys(api_keys)
            
            # 9. 显示成功信息
            print("\n🎉 账户注册完成!")
            print(f"📧 邮箱: {self.email_address}")
            print(f"🔐 密码: {password}")
            print(f"🔑 API密钥:")
            for key_info in api_keys:
                key = key_info.get('key', '')
                print(f"   {key[:15]}...{key[-8:] if len(key) > 23 else key}")
            
            return True
            
        except Exception as e:
            print(f"❌ 注册流程异常: {e}")
            return False
    
    def run_batch(self, count=1):
        """批量注册"""
        print(f"📦 开始批量注册 {count} 个账户")
        
        success_count = 0
        for i in range(count):
            print(f"\n{'='*50}")
            print(f"📋 注册进度: {i+1}/{count}")
            
            # 为每个账户创建新实例，避免数据污染
            bot = TargonSimple()
            if bot.register_single_account():
                success_count += 1
            
            # 注册间隔
            if i < count - 1:
                wait_time = 5
                print(f"⏳ 等待 {wait_time} 秒后继续...")
                time.sleep(wait_time)
        
        print(f"\n🏆 批量注册完成!")
        print(f"✅ 成功注册: {success_count}/{count}")
        
        # 统计总密钥数
        try:
            with open(self.keys_file, 'r', encoding='utf-8') as f:
                total_keys = len([line.strip() for line in f if line.strip()])
            print(f"📊 总密钥数: {total_keys}")
        except FileNotFoundError:
            print("📊 总密钥数: 0")
    
    def __del__(self):
        """清理资源"""
        try:
            self.client.close()
        except:
            pass

def show_logo():
    """显示程序logo"""
    logo = """
╔══════════════════════════════════════════════════════════════╗
║                                                              ║
║  ████████╗ █████╗ ██████╗  ██████╗  ██████╗ ███╗   ██╗       ║
║  ╚══██╔══╝██╔══██╗██╔══██╗██╔════╝ ██╔═══██╗████╗  ██║       ║
║     ██║   ███████║██████╔╝██║  ███╗██║   ██║██╔██╗ ██║       ║
║     ██║   ██╔══██║██╔══██╗██║   ██║██║   ██║██║╚██╗██║       ║
║     ██║   ██║  ██║██║  ██║╚██████╔╝╚██████╔╝██║ ╚████║       ║
║     ╚═╝   ╚═╝  ╚═╝╚═╝  ╚═╝ ╚═════╝  ╚═════╝ ╚═╝  ╚═══╝       ║
║                                                              ║
║                    🤖 注册机【躺平版】 🤖                    ║
║             💤 让注册变得简单，让躺平变得优雅 💤             ║
║                                                              ║
╚══════════════════════════════════════════════════════════════╝
    """
    print(logo)

def get_user_choice():
    """获取用户选择"""
    print("\n📋 请选择运行模式：")
    print("   1️⃣  单个账户注册 (专业版 - 带日志)")
    print("   2️⃣  批量账户注册 (专业版 - 带日志)")
    print("   3️⃣  单个账户注册 (简化版)")
    print("   4️⃣  批量账户注册 (简化版)")
    print("   0️⃣  退出程序")
    print("=" * 50)

    while True:
        try:
            choice = input("请输入选择 (0/1/2/3/4): ").strip()
            if choice in ['0', '1', '2', '3', '4']:
                return int(choice)
            else:
                print("❌ 无效选择，请输入 0、1、2、3 或 4")
        except KeyboardInterrupt:
            print("\n\n👋 程序已退出")
            return 0
        except Exception:
            print("❌ 输入错误，请重新输入")

def get_batch_count():
    """获取批量注册数量"""
    print("\n📊 批量注册设置：")
    while True:
        try:
            count = input("请输入注册账户数量 (1-100): ").strip()
            count = int(count)
            if 1 <= count <= 100:
                return count
            else:
                print("❌ 数量必须在 1-100 之间")
        except KeyboardInterrupt:
            print("\n\n👋 程序已退出")
            return 0
        except ValueError:
            print("❌ 请输入有效的数字")
        except Exception:
            print("❌ 输入错误，请重新输入")

def main():
    """主函数"""
    # 显示logo
    show_logo()
    
    # 显示程序信息
    print("🔧 程序版本：v1.1.0 (固定邮箱版)")
    print("👨‍💻 作者信息：云胡不喜@linux.do")
    print("📧 固定邮箱：<EMAIL> (基于 551543.xyz 域名)")
    print("🎯 目标平台：Targon.com")
    print("⚡ 特色功能：自动注册、手动邮箱激活、2FA设置、API密钥获取")
    
    while True:
        # 获取用户选择
        choice = get_user_choice()

        if choice == 0:
            print("\n👋 感谢使用 Targon 注册机！再见~")
            break
        elif choice == 1:
            print("\n🎯 启动单个账户注册模式 (专业版)...")
            bot = TargonRegistrar()
            bot.register_single_account()

            # 询问是否继续
            continue_choice = input("\n🤔 是否继续注册？(y/n): ").strip().lower()
            if continue_choice not in ['y', 'yes', '是']:
                print("\n👋 程序结束，感谢使用！")
                break

        elif choice == 2:
            count = get_batch_count()
            if count == 0:
                break

            print(f"\n🚀 启动批量注册模式 (专业版)，目标数量：{count}")
            print("⚠️  批量注册可能需要较长时间，请耐心等待...")

            # 确认开始
            confirm = input(f"确认开始批量注册 {count} 个账户？(y/n): ").strip().lower()
            if confirm in ['y', 'yes', '是']:
                success_count = 0
                for i in range(count):
                    print(f"\n{'='*50}")
                    print(f"📋 注册进度: {i+1}/{count}")

                    # 为每个账户创建新实例，避免数据污染
                    bot = TargonRegistrar()
                    if bot.register_single_account():
                        success_count += 1

                    # 注册间隔
                    if i < count - 1:
                        wait_time = 5
                        print(f"⏳ 等待 {wait_time} 秒后继续...")
                        time.sleep(wait_time)

                print(f"\n🏆 批量注册完成!")
                print(f"✅ 成功注册: {success_count}/{count}")

                # 统计总密钥数
                try:
                    with open("api_keys.txt", 'r', encoding='utf-8') as f:
                        total_keys = len([line.strip() for line in f if line.strip()])
                    print(f"📊 总密钥数: {total_keys}")
                except FileNotFoundError:
                    print("📊 总密钥数: 0")
            else:
                print("❌ 已取消批量注册")

            # 询问是否继续
            continue_choice = input("\n🤔 是否继续使用程序？(y/n): ").strip().lower()
            if continue_choice not in ['y', 'yes', '是']:
                print("\n👋 程序结束，感谢使用！")
                break

        elif choice == 3:
            print("\n🎯 启动单个账户注册模式 (简化版)...")
            bot = TargonSimple()
            bot.register_single_account()

            # 询问是否继续
            continue_choice = input("\n🤔 是否继续注册？(y/n): ").strip().lower()
            if continue_choice not in ['y', 'yes', '是']:
                print("\n👋 程序结束，感谢使用！")
                break

        elif choice == 4:
            count = get_batch_count()
            if count == 0:
                break

            print(f"\n🚀 启动批量注册模式 (简化版)，目标数量：{count}")
            print("⚠️  批量注册可能需要较长时间，请耐心等待...")

            # 确认开始
            confirm = input(f"确认开始批量注册 {count} 个账户？(y/n): ").strip().lower()
            if confirm in ['y', 'yes', '是']:
                bot = TargonSimple()
                bot.run_batch(count)
            else:
                print("❌ 已取消批量注册")

            # 询问是否继续
            continue_choice = input("\n🤔 是否继续使用程序？(y/n): ").strip().lower()
            if continue_choice not in ['y', 'yes', '是']:
                print("\n👋 程序结束，感谢使用！")
                break

if __name__ == "__main__":
    main()
